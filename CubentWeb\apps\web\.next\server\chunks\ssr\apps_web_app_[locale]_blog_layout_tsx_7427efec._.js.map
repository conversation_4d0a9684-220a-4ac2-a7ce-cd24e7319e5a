{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/blog/layout.tsx"], "sourcesContent": ["import type { ReactNode } from 'react';\n\ntype BlogLayoutProps = {\n  children: ReactNode;\n};\n\nexport default function BlogLayout({ children }: BlogLayoutProps) {\n  return (\n    <div className=\"blog-page\">\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,qBACE,6VAAC;QAAI,WAAU;kBACZ;;;;;;AAGP", "debugId": null}}]}