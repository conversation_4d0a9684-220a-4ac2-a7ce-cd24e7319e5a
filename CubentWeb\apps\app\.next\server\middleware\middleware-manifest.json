{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_ec43db50._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/node_modules__pnpm_16b65189._.js", "server/edge/chunks/[root-of-the-server]__6292175d._.js", "server/edge/chunks/apps_app_edge-wrapper_0144a8dc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W2GDmhqWAEvlNHlwGITKkVIGi0ZjBphzOJ0dTpjoqGM=", "__NEXT_PREVIEW_MODE_ID": "8e04ddf7fcc839578c0de1174fd1b83e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f1f1216b0bd4858dc880255b246968ca1ee6f7b69b180f7f2460c951c593945d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c729d364ddd6f6c597e75b9e17a9667a392ef76022a1ace9bdc0be44dd10fdc3"}}}, "instrumentation": null, "functions": {}}