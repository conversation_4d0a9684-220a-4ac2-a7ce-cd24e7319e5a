{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_c8462c85._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_2d5523b0._.js", "server/edge/chunks/[root-of-the-server]__b5fdeec6._.js", "server/edge/chunks/apps_web_edge-wrapper_ada886b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|images|ingest|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "9uQifFpW3FDh5bZlRviSaCJshVr+8Qe5JU48C94JGDQ=", "__NEXT_PREVIEW_MODE_ID": "0ed80575b6b126f4d1c02460cdf1812e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "633d7a6ed2024767e19d15b7566b74c0a8149d5bf230fdc180b58f5d2aa8087f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2e3e8b45f0186885a2b3de2ca8d6f7444d9f69f835579b4f38f62379888574aa"}}}, "instrumentation": null, "functions": {}}