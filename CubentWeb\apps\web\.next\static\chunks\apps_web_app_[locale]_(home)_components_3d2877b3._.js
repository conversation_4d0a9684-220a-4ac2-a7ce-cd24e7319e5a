(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/apps/web/app/[locale]/(home)/components/community.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Community": (()=>Community)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript) <export default as ExternalLink>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$github$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Github$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/github.js [app-client] (ecmascript) <export default as Github>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/message-circle.js [app-client] (ecmascript) <export default as MessageCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
// Sample community reviews data
const communityReviews = [
    {
        id: 1,
        username: 'dev_goblin',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        content: 'What @cubentbase + @nextjs is amazing! 🔥 Really excited into a proof-of-concept and already have a lot of the functionality in place 🤯 🤯 🤯',
        platform: 'twitter'
    },
    {
        id: 2,
        username: 'pixiePixels',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        content: 'Using @cubentbase I\'m really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\'t miss anything. The whole experience feels very robust and secure.',
        platform: 'twitter'
    },
    {
        id: 3,
        username: 'realJoelR',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        content: 'This weekend I made a personal record 🏆 on the last time spent creating an application with social login / permissions, database, cdn, and for free. Thanks to @cubentbase and @vercel.',
        platform: 'twitter'
    },
    {
        id: 4,
        username: 'steveOnScripts',
        avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
        content: 'Working on my own cubent project. I want this to be my first because I\'m not a backend engineer and I\'m getting it done.',
        platform: 'twitter'
    },
    {
        id: 5,
        username: 'braydenCodes',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        content: 'New to @cubentbase I was really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\'t miss anything.',
        platform: 'twitter'
    },
    {
        id: 6,
        username: 'maxbyte42',
        avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',
        content: 'Completed @cubentbase is it. It had the best experience I\'ve had with a database. Despite being a bit dubious about the fact that I have to say I really don\'t miss anything.',
        platform: 'twitter'
    },
    {
        id: 7,
        username: 'cassWritesCode',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        content: 'Biggest @cubentbase is amazing! Really impressed with the developers and see it in general.',
        platform: 'twitter'
    },
    {
        id: 8,
        username: 'alex0xFF',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',
        content: 'Just shipped my first app using @cubentbase and the developer experience is incredible! The AI features saved me hours of debugging.',
        platform: 'twitter'
    },
    {
        id: 9,
        username: 'codeKnight_',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        content: 'The context intelligence in @cubentbase is a game changer. It understands my codebase better than I do sometimes! 😅',
        platform: 'twitter'
    },
    {
        id: 10,
        username: 'mike_2stack',
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
        content: 'Been using @cubentbase for 3 months now. The AI screenshot analysis feature is mind-blowing - it catches UI issues I completely miss.',
        platform: 'twitter'
    },
    {
        id: 11,
        username: 'jennaDotJS',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        content: 'Finally found a coding assistant that actually understands context! @cubentbase has become essential to my workflow.',
        platform: 'twitter'
    },
    {
        id: 12,
        username: 'tom_is_typing',
        avatar: 'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face',
        content: 'The multi-language support in @cubentbase is fantastic. Seamlessly switching between JS, Python, and Rust in the same project.',
        platform: 'twitter'
    },
    {
        id: 13,
        username: 'anna_rmd',
        avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
        content: 'The autonomous agent mode in @cubentbase is absolutely mind-blowing. I gave it a complex task and it just... did it. No back and forth, just results.',
        platform: 'twitter'
    },
    {
        id: 14,
        username: 'liam_0x01',
        avatar: 'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=150&h=150&fit=crop&crop=face',
        content: 'Switched from Copilot to @cubentbase and the difference is night and day. The context awareness is incredible - it actually understands my project structure.',
        platform: 'twitter'
    },
    {
        id: 15,
        username: 'priyaCodesLate',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        content: 'The natural language coding in @cubentbase is revolutionary. I can literally describe what I want and it builds it perfectly. This is the future of development.',
        platform: 'twitter'
    },
    {
        id: 16,
        username: 'diego_dev',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        content: 'Documentation generation with @cubentbase saved our team weeks of work. It understands the code context and writes better docs than we ever could manually.',
        platform: 'twitter'
    },
    {
        id: 17,
        username: 'jamieStartups',
        avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
        content: 'The file operations in @cubentbase are so smart. It reads my entire workspace, understands the architecture, and makes perfect modifications across multiple files.',
        platform: 'twitter'
    },
    {
        id: 18,
        username: 'mariaAIthings',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        content: 'Terminal integration in @cubentbase is a game changer. It can run commands, analyze output, and fix issues automatically. It\'s like having a DevOps expert on call.',
        platform: 'twitter'
    },
    {
        id: 19,
        username: 'soloStone',
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
        content: 'As a solo founder, @cubentbase is my secret weapon. It handles complex coding tasks while I focus on business strategy. Productivity through the roof! 🚀',
        platform: 'twitter'
    },
    {
        id: 20,
        username: 'natSecNerd',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        content: 'The custom modes in @cubentbase are brilliant. I created a security auditor mode and it found vulnerabilities I completely missed. This tool is incredible.',
        platform: 'twitter'
    },
    {
        id: 21,
        username: 'owenWebby',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        content: 'Browser automation with @cubentbase is pure magic. It can interact with web pages, test functionality, and even debug UI issues. Mind = blown 🤯',
        platform: 'twitter'
    },
    {
        id: 22,
        username: 'aish_on_mobile',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        content: 'The code generation quality in @cubentbase is unmatched. It writes cleaner code than most junior developers and follows best practices automatically.',
        platform: 'twitter'
    },
    {
        id: 23,
        username: 'dataDan_',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',
        content: 'Using @cubentbase for data analysis scripts has been incredible. It understands pandas, numpy, and matplotlib better than I do sometimes! 📊',
        platform: 'twitter'
    },
    {
        id: 24,
        username: 'kubernick',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        content: 'The Docker and Kubernetes integration in @cubentbase is seamless. It can write deployment configs, debug container issues, and optimize resource usage.',
        platform: 'twitter'
    },
    {
        id: 25,
        username: 'rileyRunsScripts',
        avatar: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=150&h=150&fit=crop&crop=face',
        content: 'Feels like a co-pilot on steroids! @cubentbase rewrote a legacy module, added tests and opened the PR while I grabbed coffee.',
        platform: 'twitter'
    },
    {
        id: 26,
        username: 'sophDesigns',
        avatar: 'https://images.unsplash.com/photo-1552374196-c4e7ffc6e126?w=150&h=150&fit=crop&crop=face',
        content: 'Docs hero: auto-generated docs are spot-on and beautifully formatted. Onboarding new engineers is now twice as fast 🚀',
        platform: 'twitter'
    },
    {
        id: 27,
        username: 'omarBugfix',
        avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=face',
        content: 'Bug-squashing machine! Pointed @cubentbase at failing tests and it narrowed down the root cause and pushed a fix.',
        platform: 'twitter'
    },
    {
        id: 28,
        username: 'frontendChloe',
        avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=face',
        content: 'The live browser preview straight from chat saves me a ton of context-switching. My UI iterations are lightning fast ⚡️',
        platform: 'twitter'
    },
    {
        id: 29,
        username: 'rustyMara',
        avatar: 'https://images.unsplash.com/photo-1502720705749-3c045fa5b72c?w=150&h=150&fit=crop&crop=face',
        content: 'Loving @cubent.dev for Rust refactors — it split my crate into modules in seconds! 🦀',
        platform: 'twitter'
    },
    {
        id: 30,
        username: 'swiftLuke',
        avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face',
        content: '@cubent.dev fixed three memory leaks in my iOS app and explained every change. Mind = blown.',
        platform: 'twitter'
    },
    {
        id: 31,
        username: 'jsonSteele',
        avatar: 'https://images.unsplash.com/photo-1502767089025-6572583495b9?w=150&h=150&fit=crop&crop=face',
        content: 'Live context panel in @cubent.dev means I never lose track of files. Productivity +100% 💪',
        platform: 'twitter'
    },
    {
        id: 32,
        username: 'emmaUXcreates',
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face',
        content: 'The ability to generate UI components from a simple prompt is a game-changer for my workflow. @cubent.dev is amazing!',
        platform: 'twitter'
    },
    {
        id: 33,
        username: 'carterBoots',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        content: 'I use @cubent.dev to bootstrap all my new projects. It sets up the boilerplate, CI/CD pipeline, and even initial docs.',
        platform: 'twitter'
    },
    {
        id: 34,
        username: 'testZoe',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face',
        content: 'Generating complex test cases used to be a chore. Now, @cubent.dev writes them for me, covering all edge cases.',
        platform: 'twitter'
    }
];
const Community = ({ dictionary })=>{
    _s();
    const [firstColumn, secondColumn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Community.useMemo": ()=>{
            const shuffled = [
                ...communityReviews
            ].sort({
                "Community.useMemo.shuffled": ()=>Math.random() - 0.5
            }["Community.useMemo.shuffled"]);
            const midPoint = Math.ceil(shuffled.length / 2);
            return [
                shuffled.slice(0, midPoint),
                shuffled.slice(midPoint)
            ];
        }
    }["Community.useMemo"], []);
    // Create animated columns for the animation effect
    const createAnimatedColumn = (reviewsForColumn, direction)=>{
        const reviews = [
            ...reviewsForColumn,
            ...reviewsForColumn
        ]; // Duplicate for seamless loop
        const animationClass = direction === 'left' ? 'animate-scroll-left' : 'animate-scroll-right';
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `flex gap-8 ${animationClass}`,
            children: reviews.map((review, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-shrink-0 w-80 bg-neutral-700/50 backdrop-blur-sm rounded-lg p-6 border border-neutral-600/40 shadow-lg transition-all duration-300 relative overflow-hidden group",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg -z-10",
                            style: {
                                background: 'linear-gradient(135deg, #2563eb 0%, #ea580c 100%)'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 opacity-55",
                                style: {
                                    backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilterHover'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='6.5' numOctaves='16' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilterHover)'/%3E%3C/svg%3E")`,
                                    mixBlendMode: 'overlay'
                                }
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                lineNumber: 286,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                            lineNumber: 279,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                    className: "h-10 w-10 flex-shrink-0",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                            src: review.avatar,
                                            alt: review.username
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 296,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                            children: review.username.slice(0, 2).toUpperCase()
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 297,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                    lineNumber: 295,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1 min-w-0",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2 mb-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-white font-medium text-sm",
                                                    children: [
                                                        "@",
                                                        review.username
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                                    lineNumber: 301,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        viewBox: "0 0 24 24",
                                                        className: "w-4 h-4 fill-white drop-shadow-lg",
                                                        style: {
                                                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3)) drop-shadow(0 0 8px rgba(255,255,255,0.1))'
                                                        },
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                                            lineNumber: 311,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                                        lineNumber: 304,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                                    lineNumber: 303,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 300,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-neutral-300 text-sm leading-relaxed",
                                            children: review.content.replace(/@cubentbase/g, '@cubent.dev')
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 315,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                    lineNumber: 299,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                            lineNumber: 294,
                            columnNumber: 13
                        }, this)
                    ]
                }, `${review.id}-${index}`, true, {
                    fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                    lineNumber: 274,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
            lineNumber: 272,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full relative px-4 sm:px-6",
        style: {
            backgroundColor: '#161616'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-24 lg:py-36 relative overflow-hidden",
            style: {
                border: '1px solid rgba(255, 255, 255, 0.08)',
                borderTop: 'none',
                backgroundColor: 'transparent'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col items-center gap-10",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-white font-regular text-3xl tracking-tighter md:text-5xl mb-4",
                                children: "Join the community"
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                lineNumber: 339,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-neutral-400 text-lg max-w-2xl mx-auto",
                                children: "Discover what our community has to say about their Cubent experience."
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                lineNumber: 342,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                        lineNumber: 338,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col sm:flex-row gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                className: "bg-neutral-900/50 border-neutral-700 text-white hover:bg-neutral-800/50 backdrop-blur-sm",
                                asChild: true,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    href: "https://github.com/cubent/discussions",
                                    target: "_blank",
                                    rel: "noopener noreferrer",
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$github$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Github$3e$__["Github"], {
                                            className: "w-4 h-4"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 360,
                                            columnNumber: 17
                                        }, this),
                                        "GitHub discussions",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                            className: "w-3 h-3"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 362,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                    lineNumber: 354,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                lineNumber: 349,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                className: "bg-neutral-900/50 border-neutral-700 text-white hover:bg-neutral-800/50 backdrop-blur-sm",
                                asChild: true,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    href: "https://discord.gg/cubent",
                                    target: "_blank",
                                    rel: "noopener noreferrer",
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                            className: "w-4 h-4"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 376,
                                            columnNumber: 17
                                        }, this),
                                        "Discord",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                            className: "w-3 h-3"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                            lineNumber: 378,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                    lineNumber: 370,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                lineNumber: 365,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                        lineNumber: 348,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full max-w-7xl mx-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-12 mask-gradient",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col gap-8",
                                children: [
                                    createAnimatedColumn(firstColumn, 'left'),
                                    createAnimatedColumn(secondColumn, 'right')
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                                lineNumber: 387,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                            lineNumber: 385,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                        lineNumber: 384,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
                lineNumber: 336,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
            lineNumber: 328,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/app/[locale]/(home)/components/community.tsx",
        lineNumber: 327,
        columnNumber: 5
    }, this);
};
_s(Community, "LLtX4pbOkPR3YqZFBMTfEBjUMQE=");
_c = Community;
var _c;
__turbopack_context__.k.register(_c, "Community");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/app/[locale]/(home)/components/download.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Download": (()=>Download)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
'use client';
;
;
;
const Download = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full relative px-4 sm:px-6",
        style: {
            backgroundColor: '#161616'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-24 lg:py-36 relative overflow-hidden",
            style: {
                border: '1px solid rgba(255, 255, 255, 0.08)',
                borderTop: 'none',
                backgroundColor: 'transparent'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col items-center gap-8 max-w-4xl mx-auto text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gradient-to-r from-blue-600 via-orange-500 to-blue-400 bg-clip-text text-transparent text-sm font-medium tracking-wider uppercase",
                                children: "— Let's Code"
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                lineNumber: 20,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-white font-regular text-3xl tracking-tighter md:text-5xl leading-tight",
                                children: [
                                    "Install our extension and",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                        lineNumber: 24,
                                        columnNumber: 40
                                    }, this),
                                    "start coding today"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                lineNumber: 23,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-white/70 text-lg leading-relaxed max-w-2xl mx-auto",
                                children: "Get started with AI-powered development. Choose your preferred editor and experience the future of coding."
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                lineNumber: 27,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                        lineNumber: 19,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-center w-full max-w-md",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            size: "lg",
                            className: "bg-neutral-700/50 backdrop-blur-sm text-white border-0 rounded-lg px-8 py-4 text-lg font-medium transition-all duration-300 hover:scale-105 hover:bg-neutral-600/60",
                            asChild: true,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "https://marketplace.visualstudio.com/items?itemName=cubent.cubent",
                                target: "_blank",
                                rel: "noopener noreferrer",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                            className: "w-6 h-6",
                                            viewBox: "0 0 24 24",
                                            fill: "currentColor",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                                lineNumber: 46,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                            lineNumber: 45,
                                            columnNumber: 19
                                        }, this),
                                        "VS Code"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                    lineNumber: 44,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                                lineNumber: 39,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                            lineNumber: 34,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                        lineNumber: 33,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
                lineNumber: 17,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
            lineNumber: 9,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/app/[locale]/(home)/components/download.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
};
_c = Download;
var _c;
__turbopack_context__.k.register(_c, "Download");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Mockup": (()=>Mockup),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
// Feature data
const features = [
    {
        id: 1,
        title: "Sit back while it handles the heavy lifting, step by step",
        image: "/images/cubent-feature-1.png",
        alt: "Context Intelligence",
        description: "Let Cubent chain terminal commands, edit files, run tests and open pull requests while you sip your coffee—true hands-free automation.",
        content: [
            "Skip the boilerplate. Cubent queues terminal commands, edits files, runs tests and even opens pull-requests in a single autonomous flow. Cubent revolutionizes this experience by providing deep, contextual understanding of your entire project ecosystem. Our advanced AI doesn't just read your code—it comprehends the intricate relationships between components, understands your architectural decisions, and learns from your coding patterns.",
            "Unlike traditional code assistants that work in isolation, Cubent maintains a comprehensive map of your project's structure, dependencies, and design patterns. This enables it to make suggestions that aren't just syntactically correct, but architecturally sound and consistent with your existing codebase. Whether you're working on a microservices architecture, a monolithic application, or a complex distributed system, Cubent adapts to your specific context.",
            "The intelligence extends beyond simple code completion. Cubent analyzes cross-file dependencies, understands the impact of changes across your entire system, and can predict potential issues before they arise. This proactive approach to development helps teams maintain code quality while accelerating their development velocity, making it an indispensable tool for serious product development teams."
        ]
    },
    {
        id: 2,
        title: "Stay in sync with a real-time panel that knows your project",
        image: "/images/cubent-feature-2.png",
        alt: "AI Screenshot Analysis",
        description: "A single overlay that pulses with your repo’s heartbeat—files, problems, Git, terminals and folders always a glance away.",
        content: [
            "Never lose your place again. The context panel surfaces open files, failing tests, Git diffs, terminals and folders in one glanceable hub. Cubent's revolutionary screenshot-to-code technology bridges the gap between design and implementation, allowing developers to transform visual mockups into functional code in seconds rather than hours.",
            "Our advanced computer vision algorithms analyze every pixel of your designs, understanding not just what elements are present, but how they should behave and interact. The system recognizes common UI patterns, understands responsive design principles, and generates code that follows modern best practices. Whether you're working with Figma designs, hand-drawn sketches, or competitor screenshots, Cubent can interpret and implement them accurately.",
            "The generated code isn't just a static representation—it's production-ready, accessible, and optimized for performance. Cubent automatically handles responsive breakpoints, generates semantic HTML, applies appropriate ARIA labels, and ensures cross-browser compatibility. This means you can go from concept to working prototype in minutes, allowing for rapid iteration and faster time-to-market for your products."
        ]
    },
    {
        id: 3,
        title: "Instant browser preview & testing",
        image: "/images/cubent-feature-3.png",
        alt: "Smart Code Editing",
        description: "Spin up a live browser, snap screenshots and sanity-check UX—all without ever Alt-Tabbing out of your editor.",
        content: [
            "Request a ‘preview’ in chat and Cubent spins up a local server, opens a browser preview and streams logs back to you. Cubent's intelligent editing capabilities go far beyond traditional autocomplete, offering a sophisticated understanding of code quality, performance implications, and best practices. Every suggestion is crafted with the goal of not just making your code work, but making it exceptional.",
            "The system continuously analyzes your code for potential improvements, from micro-optimizations that enhance performance to architectural suggestions that improve maintainability. Cubent understands the nuances of different programming languages, frameworks, and design patterns, allowing it to provide highly specific and relevant recommendations tailored to your technology stack and coding style.",
            "What sets Cubent apart is its ability to learn and adapt to your team's specific standards and preferences. It recognizes your coding conventions, understands your project's unique requirements, and evolves its suggestions to match your team's definition of perfect code. This results in a more consistent codebase, reduced technical debt, and a development experience that feels truly personalized and intelligent."
        ]
    }
];
const Mockup = ()=>{
    _s();
    const [selectedFeature, setSelectedFeature] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const imagesScrollRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const textScrollRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Sync scroll between images and text containers
    const syncScroll = (source, target)=>{
        target.scrollLeft = source.scrollLeft;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Mockup.useEffect": ()=>{
            const imagesContainer = imagesScrollRef.current;
            const textContainer = textScrollRef.current;
            if (!imagesContainer || !textContainer) return;
            const handleImagesScroll = {
                "Mockup.useEffect.handleImagesScroll": ()=>syncScroll(imagesContainer, textContainer)
            }["Mockup.useEffect.handleImagesScroll"];
            const handleTextScroll = {
                "Mockup.useEffect.handleTextScroll": ()=>syncScroll(textContainer, imagesContainer)
            }["Mockup.useEffect.handleTextScroll"];
            imagesContainer.addEventListener('scroll', handleImagesScroll);
            textContainer.addEventListener('scroll', handleTextScroll);
            return ({
                "Mockup.useEffect": ()=>{
                    imagesContainer.removeEventListener('scroll', handleImagesScroll);
                    textContainer.removeEventListener('scroll', handleTextScroll);
                }
            })["Mockup.useEffect"];
        }
    }["Mockup.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full relative px-4 sm:px-6",
                style: {
                    backgroundColor: '#161616'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "hidden flex-col items-center justify-center gap-2 py-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative w-full max-w-8xl",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative overflow-hidden",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: "/images/Cubent.Dev.webp",
                                    alt: "Cubent Editor Interface - Code editing with AI assistance",
                                    width: 1200,
                                    height: 800,
                                    className: "w-full h-auto object-cover rounded-lg",
                                    priority: true
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 87,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-30 -z-10"
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 96,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 86,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                        lineNumber: 85,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                    lineNumber: 84,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                lineNumber: 83,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full relative px-4 sm:px-6",
                style: {
                    backgroundColor: '#161616'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 pt-24 lg:pt-36 pb-0 relative",
                    style: {
                        border: '1px solid rgba(255, 255, 255, 0.08)',
                        borderTop: 'none',
                        backgroundColor: 'transparent'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 flex flex-col lg:flex-row gap-10 lg:gap-20 items-start mb-20 lg:mb-24",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1 max-w-md",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-4xl lg:text-5xl font-regular tracking-tighter text-white",
                                        children: "An AI that vibes with your code and keeps up with your flow."
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 117,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 116,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1 max-w-lg",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-lg text-muted-foreground leading-relaxed",
                                        children: "Cubent transforms how developers work by providing intelligent, context-aware assistance that learns from your codebase. From instant screenshot-to-code conversion to deep architectural understanding, we're building the future of software development."
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 124,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 123,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 114,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 mb-20 lg:mb-24 -mx-6 sm:-mx-8 lg:-mx-12",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-16 lg:h-20 w-full relative overflow-hidden",
                                style: {
                                    backgroundColor: '#161616'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute top-0 left-0 right-0 h-px",
                                        style: {
                                            backgroundColor: 'rgba(255,255,255,0.08)'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 134,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute inset-0",
                                        style: {
                                            backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',
                                            backgroundSize: '8px 100%'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 139,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute bottom-0 left-0 right-0 h-px",
                                        style: {
                                            backgroundColor: 'rgba(255,255,255,0.08)'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 147,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                lineNumber: 132,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 131,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 -mt-20 lg:-mt-24 mb-8 -mx-6 sm:-mx-8 lg:-mx-12",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    ref: imagesScrollRef,
                                    className: "hidden md:grid md:grid-cols-2 md:gap-0",
                                    style: {
                                        backgroundColor: '#161616'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: "/images/4.png",
                                                alt: "Stay in sync with a real-time panel that knows your project",
                                                width: 800,
                                                height: 600,
                                                className: "w-full h-auto object-cover border border-gray-600"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                lineNumber: 160,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 159,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: "/images/3.png",
                                                alt: "Sit back while it handles the heavy lifting, step by step",
                                                width: 800,
                                                height: 600,
                                                className: "w-full h-auto object-cover border border-gray-600"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                lineNumber: 170,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 169,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 157,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    ref: textScrollRef,
                                    className: "md:grid md:grid-cols-2 md:gap-8 flex md:flex-none overflow-x-auto md:overflow-x-visible snap-x snap-mandatory md:snap-none px-6 sm:px-8 lg:px-12 mt-16 mb-0 relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "hidden md:block absolute left-1/2 top-0 bottom-0 w-px bg-white/10 transform -translate-x-1/2"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 183,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0 w-[85vw] md:w-auto snap-center md:snap-align-none max-w-md mx-auto text-left",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "block md:hidden mb-6",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        src: "/images/4.png",
                                                        alt: "Stay in sync with a real-time panel that knows your project",
                                                        width: 800,
                                                        height: 600,
                                                        className: "w-full h-auto object-cover border border-gray-600 rounded"
                                                    }, void 0, false, {
                                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                        lineNumber: 189,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 188,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "text-4xl font-normal text-white/90 mb-6 leading-tight",
                                                    children: "Stay in sync with a real-time panel that knows your project"
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 197,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-400 text-sm leading-relaxed pb-6",
                                                    children: "A single overlay that pulses with your repo's heartbeat—files, problems, Git, terminals and folders always a glance away."
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 200,
                                                    columnNumber: 13
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 186,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0 w-[85vw] md:w-auto snap-center md:snap-align-none max-w-md mx-auto text-left",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "block md:hidden mb-6",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        src: "/images/3.png",
                                                        alt: "Sit back while it handles the heavy lifting, step by step",
                                                        width: 800,
                                                        height: 600,
                                                        className: "w-full h-auto object-cover border border-gray-600 rounded"
                                                    }, void 0, false, {
                                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                        lineNumber: 208,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 207,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "text-4xl font-normal text-white/90 mb-6 leading-tight",
                                                    children: "Sit back while it handles the heavy lifting, step by step"
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 216,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-400 text-sm leading-relaxed pb-6",
                                                    children: "Let Cubent chain terminal commands, edit files, run tests and open pull requests while you sip your coffee—true hands-free automation."
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 219,
                                                    columnNumber: 13
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 205,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 181,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative z-10 mt-16",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-16 lg:h-20 w-full relative overflow-hidden",
                                        style: {
                                            backgroundColor: '#161616'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute top-0 left-0 right-0 h-px",
                                                style: {
                                                    backgroundColor: 'rgba(255,255,255,0.08)'
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                lineNumber: 229,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute inset-0",
                                                style: {
                                                    backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',
                                                    backgroundSize: '8px 100%'
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                lineNumber: 234,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute bottom-0 left-0 right-0 h-px",
                                                style: {
                                                    backgroundColor: 'rgba(255,255,255,0.08)'
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                lineNumber: 242,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 227,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 226,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 155,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                    lineNumber: 104,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                lineNumber: 103,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full relative px-4 sm:px-6",
                style: {
                    backgroundColor: '#161616'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 pt-24 lg:pt-36 pb-12 lg:pb-16 relative",
                    style: {
                        border: '1px solid rgba(255, 255, 255, 0.08)',
                        borderTop: 'none',
                        backgroundColor: 'transparent'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 lg:grid-cols-2 gap-0 items-start",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-8 pr-0 lg:pr-20 py-4 -mt-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "block lg:hidden mb-6",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                src: "/images/Cubent.Dev (52).png",
                                                alt: "Cubent MCP Tools interface",
                                                className: "w-full h-auto object-cover border border-gray-600 rounded"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                lineNumber: 269,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 268,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-white/60 text-sm font-medium tracking-wider",
                                            children: "— MCP Tools"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 277,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-4xl lg:text-5xl font-regular tracking-tight text-white leading-tight",
                                                    children: "Integrate with the tools you already use"
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 283,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-white/70 text-lg leading-relaxed max-w-md",
                                                    children: "Cubent brings together your essential apps with MCP from GitHub to Notion — into one powerful interface. Discover, connect, and explore your data like never before."
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 286,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 282,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 266,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "hidden lg:block relative h-full -mr-6 sm:-mr-8 lg:-mr-12",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "aspect-square w-full absolute -top-24 lg:-top-36 right-0",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            src: "/images/Cubent.Dev (52).png",
                                            alt: "Cubent MCP Tools interface",
                                            className: "w-full h-full object-cover"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 295,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 294,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 293,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 264,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 mt-32 lg:mt-36 -mx-6 sm:-mx-8 lg:-mx-12",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-16 lg:h-20 w-full relative overflow-hidden",
                                style: {
                                    backgroundColor: '#161616'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute top-0 left-0 right-0 h-px",
                                        style: {
                                            backgroundColor: 'rgba(255,255,255,0.08)'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 308,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute inset-0",
                                        style: {
                                            backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',
                                            backgroundSize: '8px 100%'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 313,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute bottom-0 left-0 right-0 h-px",
                                        style: {
                                            backgroundColor: 'rgba(255,255,255,0.08)'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 321,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                lineNumber: 306,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 305,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                    lineNumber: 254,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                lineNumber: 253,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full relative px-4 sm:px-6",
                style: {
                    backgroundColor: '#161616'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-24 lg:py-36 pb-16 lg:pb-20 relative",
                    style: {
                        border: '1px solid rgba(255, 255, 255, 0.08)',
                        borderTop: 'none',
                        backgroundColor: 'transparent'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 lg:grid-cols-2 gap-0 items-start",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "hidden lg:block relative h-full -ml-6 sm:-ml-8 lg:-ml-12",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "aspect-square w-full absolute -top-24 lg:-top-36 left-0",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            src: "/images/Cubent.Dev (53).png",
                                            alt: "Cubent Autocomplete interface",
                                            className: "w-full h-full object-cover"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 347,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 346,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 345,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-8 pl-0 lg:pl-20 py-4 -mt-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "block lg:hidden mb-6",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                src: "/images/Cubent.Dev (53).png",
                                                alt: "Cubent Autocomplete interface",
                                                className: "w-full h-auto object-cover border border-gray-600 rounded"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                lineNumber: 359,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 358,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-white/60 text-sm font-medium tracking-wider",
                                            children: "— Autocomplete"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 367,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-4xl lg:text-5xl font-regular tracking-tight text-white leading-tight",
                                                    children: "Code smarter with context-aware autocomplete"
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 373,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-white/70 text-lg leading-relaxed max-w-md",
                                                    children: "Experience intelligent code completion that understands your project context. Cubent's autocomplete adapts to your coding patterns and suggests the most relevant completions — just start typing, and let the magic happen."
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                                    lineNumber: 376,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 372,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 356,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 343,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 mt-16 lg:mt-20 -mx-6 sm:-mx-8 lg:-mx-12",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-16 lg:h-20 w-full relative overflow-hidden",
                                style: {
                                    backgroundColor: '#161616'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute top-0 left-0 right-0 h-px",
                                        style: {
                                            backgroundColor: 'rgba(255,255,255,0.08)'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 387,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute inset-0",
                                        style: {
                                            backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',
                                            backgroundSize: '8px 100%'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 392,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute bottom-0 left-0 right-0 h-px",
                                        style: {
                                            backgroundColor: 'rgba(255,255,255,0.08)'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 400,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                lineNumber: 385,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 384,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                    lineNumber: 334,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                lineNumber: 333,
                columnNumber: 5
            }, this),
            selectedFeature && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-[#1a1a1a] rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setSelectedFeature(null),
                                    className: "absolute top-6 right-6 z-10 w-10 h-10 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white/70 hover:text-white transition-all duration-200",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$511$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                        size: 20
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 419,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 415,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative h-80 w-full overflow-hidden",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: selectedFeature.image,
                                            alt: selectedFeature.alt,
                                            fill: true,
                                            className: "object-cover"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 424,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute inset-0 bg-gradient-to-t from-[#1a1a1a] via-transparent to-transparent"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 430,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 423,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 414,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-8 max-h-[50vh] overflow-y-auto",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-3xl font-bold text-white mb-6",
                                    children: selectedFeature.title
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 436,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-300 text-lg mb-8 leading-relaxed",
                                    children: selectedFeature.description
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 440,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-6",
                                    children: selectedFeature.content.map((paragraph, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-300 leading-relaxed text-base",
                                            children: paragraph
                                        }, index, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                            lineNumber: 446,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 444,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-8 pt-6 border-t border-white/10",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-400 text-sm leading-relaxed",
                                        children: "Experience the power of AI-driven development with Cubent's advanced features designed to accelerate your workflow and improve code quality. Join thousands of developers who have already transformed their development process with intelligent, context-aware coding assistance."
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                        lineNumber: 453,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                                    lineNumber: 452,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                            lineNumber: 435,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                    lineNumber: 412,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/mockup.tsx",
                lineNumber: 411,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(Mockup, "CHVu583ducQtEFFEDAjuxx+Ar78=");
_c = Mockup;
const __TURBOPACK__default__export__ = Mockup;
var _c;
__turbopack_context__.k.register(_c, "Mockup");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SpeedSection": (()=>SpeedSection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_ec182a26539ad5b881a342e51a7201a6/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
'use client';
;
const SpeedSection = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "hidden md:block w-full relative px-4 sm:px-6",
            style: {
                backgroundColor: '#161616'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-16 lg:py-20 relative",
                style: {
                    border: '1px solid rgba(255, 255, 255, 0.08)',
                    borderTop: 'none',
                    backgroundColor: 'transparent'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col lg:flex-row items-center justify-between gap-8 mb-16 lg:mb-20",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-4xl lg:text-5xl font-regular tracking-tight text-white leading-tight mb-4",
                                        children: "Speed with context awareness"
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                        lineNumber: 18,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-white/70 text-lg leading-relaxed max-w-lg",
                                        children: "With a Time-to-First-Audio of 40ms, Sonic is the fastest generative voice model built for streaming."
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                        lineNumber: 21,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                lineNumber: 17,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "hidden md:flex flex-shrink-0 text-right",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-4xl md:text-6xl lg:text-7xl font-normal leading-none",
                                            style: {
                                                background: 'linear-gradient(135deg, #ff8c00, #ff4500)',
                                                WebkitBackgroundClip: 'text',
                                                WebkitTextFillColor: 'transparent',
                                                backgroundClip: 'text'
                                            },
                                            children: "*145%"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                            lineNumber: 29,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-white/60 text-sm mt-2",
                                            children: "faster than the next best competitor"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                            lineNumber: 40,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                    lineNumber: 28,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                lineNumber: 27,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                        lineNumber: 15,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full flex justify-center mb-6 md:mb-8 lg:mb-10 -mx-4 md:mx-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                            src: "/images/Cubent.Dev (30).png",
                            alt: "Cubent speed demonstration",
                            className: "w-[130%] md:w-full max-w-none md:max-w-6xl h-auto object-contain"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                            lineNumber: 49,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                        lineNumber: 48,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "md:hidden text-center mb-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-2xl font-normal leading-none mb-2",
                                style: {
                                    background: 'linear-gradient(135deg, #ff8c00, #ff4500)',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                    backgroundClip: 'text'
                                },
                                children: "*145%"
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                lineNumber: 58,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-white/60 text-xs",
                                children: "faster than the next best competitor"
                            }, void 0, false, {
                                fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                        lineNumber: 57,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative z-10 -mx-6 sm:-mx-8 lg:-mx-12",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-16 lg:h-20 w-full relative overflow-hidden",
                            style: {
                                backgroundColor: '#161616'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute top-0 left-0 right-0 h-px",
                                    style: {
                                        backgroundColor: 'rgba(255,255,255,0.08)'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                    lineNumber: 78,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0",
                                    style: {
                                        backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',
                                        backgroundSize: '8px 100%'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_ec182a26539ad5b881a342e51a7201a6$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute bottom-0 left-0 right-0 h-px",
                                    style: {
                                        backgroundColor: 'rgba(255,255,255,0.08)'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                                    lineNumber: 91,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                            lineNumber: 76,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
                lineNumber: 7,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx",
            lineNumber: 6,
            columnNumber: 5
        }, this)
    }, void 0, false);
};
_c = SpeedSection;
var _c;
__turbopack_context__.k.register(_c, "SpeedSection");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=apps_web_app_%5Blocale%5D_%28home%29_components_3d2877b3._.js.map